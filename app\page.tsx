"use client"

import React, { useState, useEffect } from 'react';
import { MessageSquare, <PERSON><PERSON>les, Hash, CheckCircle2, ArrowRight, Send, RotateCcw, Co<PERSON>, ExternalLink } from 'lucide-react';

type FlowState = 'input' | 'generating' | 'channel' | 'processing' | 'success' | 'actions';

const SAMPLE_CONTENT = `Based on your request, here's a comprehensive analysis`;

function App() {
  const [currentState, setCurrentState] = useState<FlowState>('input');
  const [inputValue, setInputValue] = useState('');
  const [channelName, setChannelName] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');
  const [streamingIndex, setStreamingIndex] = useState(0);
  const [progress, setProgress] = useState(0);

  // Handle content streaming
  useEffect(() => {
    if (currentState === 'generating' && streamingIndex < SAMPLE_CONTENT.length) {
      const timeout = setTimeout(() => {
        setGeneratedContent(SAMPLE_CONTENT.slice(0, streamingIndex + 1));
        setStreamingIndex(prev => prev + 1);
      }, 20 + Math.random() * 30);
      
      return () => clearTimeout(timeout);
    } else if (currentState === 'generating' && streamingIndex >= SAMPLE_CONTENT.length) {
      setTimeout(() => setCurrentState('channel'), 1000);
    }
  }, [currentState, streamingIndex]);

  // Handle processing progress
  useEffect(() => {
    if (currentState === 'processing') {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setTimeout(() => setCurrentState('success'), 500);
            return 100;
          }
          return prev + Math.random() * 12 + 3;
        });
      }, 150);
      
      return () => clearInterval(interval);
    }
  }, [currentState]);

  const handleStartGeneration = () => {
    if (inputValue.trim()) {
      setCurrentState('generating');
      setStreamingIndex(0);
      setGeneratedContent('');
    }
  };

  const handleChannelSubmit = () => {
    if (channelName.trim()) {
      setCurrentState('processing');
      setProgress(0);
    }
  };

  const handleRestart = () => {
    setCurrentState('input');
    setInputValue('');
    setChannelName('');
    setGeneratedContent('');
    setStreamingIndex(0);
    setProgress(0);
  };

  const handleFinish = () => {
    setCurrentState('actions');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        
        {/* State 1: Input Request */}
        <div className={`transition-all duration-500 transform ${
          currentState === 'input' 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 -translate-y-4 pointer-events-none absolute'
        }`}>
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-900 rounded-xl mb-4">
                <MessageSquare className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-2">Slack Content Generator</h1>
              <p className="text-gray-600">Describe what you&apos;d like to communicate</p>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  What would you like to share?
                </label>
                <textarea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="e.g., Quarterly performance update for the team..."
                  className="w-full h-32 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent resize-none text-gray-900 placeholder-gray-500"
                />
              </div>
              
              <button
                onClick={handleStartGeneration}
                disabled={!inputValue.trim()}
                className="w-full bg-gray-900 hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <Sparkles className="w-5 h-5" />
                <span>Generate Content</span>
              </button>
            </div>
          </div>
        </div>

        {/* State 2: Content Generation */}
        <div className={`transition-all duration-500 transform ${
          currentState === 'generating' 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4 pointer-events-none absolute'
        }`}>
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white animate-pulse" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Generating content...</h2>
                <p className="text-sm text-gray-600">AI is crafting your message</p>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
              <div className="text-gray-900 leading-relaxed">
                {generatedContent}
                <span className="inline-block w-2 h-5 bg-gray-900 ml-1 animate-pulse"></span>
              </div>
            </div>
          </div>
        </div>

        {/* State 3: Channel Selection */}
        <div className={`transition-all duration-500 transform ${
          currentState === 'channel' 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4 pointer-events-none absolute'
        }`}>
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-900 rounded-xl mb-4">
                <Hash className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose Destination</h2>
              <p className="text-gray-600">Select which Slack channel to post to</p>
            </div>

            <div className="bg-gray-50 rounded-xl p-6 mb-6 border border-gray-100">
              <p className="text-sm text-gray-600 mb-3">Generated content preview:</p>
              <div className="text-gray-900 text-sm leading-relaxed line-clamp-3">
                {generatedContent}
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Channel name
                </label>
                <div className="relative">
                  <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={channelName}
                    onChange={(e) => setChannelName(e.target.value)}
                    placeholder="general"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-gray-900 focus:border-transparent text-gray-900 placeholder-gray-500"
                  />
                </div>
              </div>
              
              <button
                onClick={handleChannelSubmit}
                disabled={!channelName.trim()}
                className="w-full bg-gray-900 hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <Send className="w-5 h-5" />
                <span>Post to Channel</span>
              </button>
            </div>
          </div>
        </div>

        {/* State 4: Processing */}
        <div className={`transition-all duration-500 transform ${
          currentState === 'processing' 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4 pointer-events-none absolute'
        }`}>
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-900 rounded-xl mb-4">
                <Send className="w-6 h-6 text-white animate-pulse" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Posting to Slack</h2>
              <p className="text-gray-600">Sending your message to #{channelName}</p>
            </div>

            <div className="space-y-6">
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm font-medium text-gray-700">Progress</span>
                  <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div 
                    className="h-2 bg-gray-900 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <div className="w-8 h-8 bg-green-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                    <CheckCircle2 className="w-5 h-5 text-white" />
                  </div>
                  <p className="text-xs text-gray-600">Content Ready</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg mx-auto mb-2 animate-pulse"></div>
                  <p className="text-xs text-gray-600">Connecting</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-100">
                  <div className="w-8 h-8 bg-gray-300 rounded-lg mx-auto mb-2"></div>
                  <p className="text-xs text-gray-600">Pending</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* State 5: Success */}
        <div className={`transition-all duration-500 transform ${
          currentState === 'success' 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4 pointer-events-none absolute'
        }`}>
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-2xl mb-4">
                <CheckCircle2 className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Message Posted Successfully</h2>
              <p className="text-gray-600">Your content has been shared in #{channelName}</p>
            </div>

            <div className="bg-green-50 rounded-xl p-6 border border-green-100 mb-6">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <Hash className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-green-800 mb-1">#{channelName}</p>
                  <p className="text-sm text-green-700 line-clamp-2">{generatedContent}</p>
                </div>
              </div>
            </div>

            <button
              onClick={handleFinish}
              className="w-full bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
            >
              <span>Continue</span>
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* State 6: Actions */}
        <div className={`transition-all duration-500 transform ${
          currentState === 'actions' 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4 pointer-events-none absolute'
        }`}>
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">What&apos;s Next?</h2>
              <p className="text-gray-600">Choose your next action</p>
            </div>

            <div className="grid gap-4 mb-6">
              <button className="group bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 rounded-xl p-6 transition-all duration-200 text-left">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
                    <ExternalLink className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">View in Slack</p>
                    <p className="text-sm text-gray-600">Open the message in your Slack workspace</p>
                  </div>
                </div>
              </button>

              <button className="group bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 rounded-xl p-6 transition-all duration-200 text-left">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-700 rounded-xl flex items-center justify-center">
                    <Copy className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 group-hover:text-gray-700 transition-colors">Copy Content</p>
                    <p className="text-sm text-gray-600">Copy the generated message to clipboard</p>
                  </div>
                </div>
              </button>
            </div>

            <div className="flex space-x-3">
              <button 
                onClick={handleRestart}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <RotateCcw className="w-5 h-5" />
                <span>Create Another</span>
              </button>
              
              <button className="bg-gray-900 hover:bg-gray-800 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200">
                Done
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}

export default App;